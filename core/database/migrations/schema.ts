import { pgTable, index, foreignKey, varchar, timestamp, text, boolean, type AnyPgColumn, numeric, integer, unique } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"



export const activityLogs = pgTable("activity_logs", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	userId: varchar("user_id"),
	entityType: varchar("entity_type", { length: 50 }).notNull(),
	entityId: varchar("entity_id", { length: 255 }).notNull(),
	action: varchar({ length: 50 }).notNull(),
	oldValues: text("old_values"),
	newValues: text("new_values"),
	ipAddress: varchar("ip_address", { length: 255 }),
	userAgent: varchar("user_agent", { length: 500 }),
}, (table) => [
	index("idx_activity_org_entity").using("btree", table.organizationId.asc().nullsLast().op("text_ops"), table.entityType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "activity_logs_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "activity_logs_user_id_users_id_fk"
		}).onDelete("set null"),
]);

export const calendarEvents = pgTable("calendar_events", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	title: varchar({ length: 255 }).notNull(),
	description: text(),
	startTime: timestamp("start_time", { mode: 'string' }).notNull(),
	endTime: timestamp("end_time", { mode: 'string' }).notNull(),
	isAllDay: boolean("is_all_day").default(false),
	timezone: varchar({ length: 100 }).default('UTC'),
	entityType: varchar("entity_type", { length: 50 }),
	entityId: varchar("entity_id", { length: 255 }),
	externalCalendarId: varchar("external_calendar_id", { length: 255 }),
	externalEventId: varchar("external_event_id", { length: 255 }),
	syncProvider: varchar("sync_provider", { length: 50 }),
	lastSyncAt: timestamp("last_sync_at", { mode: 'string' }),
	createdBy: varchar("created_by").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "calendar_events_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "calendar_events_created_by_users_id_fk"
		}).onDelete("cascade"),
]);

export const clientBillingAddress = pgTable("client_billing_address", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	clientId: varchar("client_id").notNull(),
	name: varchar({ length: 255 }).notNull(),
	line1: varchar({ length: 255 }).notNull(),
	line2: varchar({ length: 255 }),
	city: varchar({ length: 255 }).notNull(),
	state: varchar({ length: 255 }).notNull(),
	zip: varchar({ length: 255 }).notNull(),
	country: varchar({ length: 255 }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "client_billing_address_client_id_clients_id_fk"
		}).onDelete("cascade"),
]);

export const clientContacts = pgTable("client_contacts", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	clientId: varchar("client_id").notNull(),
	name: varchar({ length: 255 }).notNull(),
	email: varchar({ length: 255 }),
	phone: varchar({ length: 50 }),
	position: varchar({ length: 255 }),
	isPrimary: boolean("is_primary").default(false),
}, (table) => [
	foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "client_contacts_client_id_clients_id_fk"
		}).onDelete("cascade"),
]);

export const clientIntegrationAccess = pgTable("client_integration_access", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	clientId: varchar("client_id").notNull(),
	integrationId: varchar("integration_id").notNull(),
	accessLevel: varchar("access_level", { length: 20 }).default('read'),
	grantedBy: varchar("granted_by").notNull(),
	grantedAt: timestamp("granted_at", { mode: 'string' }).notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "client_integration_access_client_id_clients_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.integrationId],
			foreignColumns: [integrations.id],
			name: "client_integration_access_integration_id_integrations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.grantedBy],
			foreignColumns: [users.id],
			name: "client_integration_access_granted_by_users_id_fk"
		}).onDelete("cascade"),
]);

export const clientNotifications = pgTable("client_notifications", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	userId: varchar("user_id").notNull(),
	type: varchar({ length: 50 }).notNull(),
	title: varchar({ length: 255 }).notNull(),
	message: text().notNull(),
	isRead: boolean("is_read").default(false),
	readAt: timestamp("read_at", { mode: 'string' }),
	actionUrl: varchar("action_url", { length: 500 }),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "client_notifications_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const clientOrganizations = pgTable("client_organizations", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	clientId: varchar("client_id").notNull(),
	organizationId: varchar("organization_id").notNull(),
	relationshipType: varchar("relationship_type", { length: 50 }).default('client'),
	status: varchar({ length: 20 }).default('active'),
	startDate: timestamp("start_date", { mode: 'string' }).defaultNow().notNull(),
	endDate: timestamp("end_date", { mode: 'string' }),
	notes: text(),
}, (table) => [
	foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "client_organizations_client_id_clients_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "client_organizations_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
]);

export const clientPreferences = pgTable("client_preferences", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	clientId: varchar("client_id").notNull(),
	communicationMethod: varchar("communication_method", { length: 50 }).default('email'),
	timezone: varchar({ length: 100 }).default('UTC'),
	language: varchar({ length: 10 }).default('en'),
	notificationSettings: text("notification_settings"),
}, (table) => [
	foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "client_preferences_client_id_clients_id_fk"
		}).onDelete("cascade"),
]);

export const accounts = pgTable("accounts", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	accountId: varchar("account_id", { length: 255 }).notNull(),
	providerId: varchar("provider_id", { length: 255 }).notNull(),
	userId: varchar("user_id").notNull(),
	accessToken: varchar("access_token", { length: 255 }),
	refreshToken: varchar("refresh_token", { length: 255 }),
	idToken: varchar("id_token", { length: 255 }),
	accessTokenExpiresAt: timestamp("access_token_expires_at", { mode: 'string' }),
	refreshTokenExpiresAt: timestamp("refresh_token_expires_at", { mode: 'string' }),
	scope: varchar({ length: 255 }),
	password: varchar({ length: 255 }),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "accounts_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const contractTemplates = pgTable("contract_templates", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	name: varchar({ length: 255 }).notNull(),
	content: text().notNull(),
	variables: text(),
	isActive: boolean("is_active").default(true),
	createdBy: varchar("created_by").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "contract_templates_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "contract_templates_created_by_users_id_fk"
		}).onDelete("cascade"),
]);

export const clients = pgTable("clients", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	name: varchar({ length: 255 }).notNull(),
	companyName: varchar("company_name", { length: 255 }),
	email: varchar({ length: 255 }),
	phone: varchar({ length: 50 }),
	website: varchar({ length: 255 }),
	description: text(),
	avatar: varchar({ length: 255 }),
	status: varchar({ length: 20 }).default('active'),
	ownerId: varchar("owner_id").notNull(),
	organizationId: varchar("organization_id").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.ownerId],
			foreignColumns: [users.id],
			name: "clients_owner_id_users_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "clients_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
]);

export const contracts = pgTable("contracts", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	clientId: varchar("client_id").notNull(),
	projectId: varchar("project_id"),
	templateId: varchar("template_id"),
	title: varchar({ length: 255 }).notNull(),
	content: text().notNull(),
	status: varchar({ length: 20 }).default('draft'),
	signedDate: timestamp("signed_date", { mode: 'string' }),
	expiresDate: timestamp("expires_date", { mode: 'string' }),
	value: numeric({ precision: 10, scale:  2 }),
	currency: varchar({ length: 3 }).default('USD'),
	createdBy: varchar("created_by").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "contracts_client_id_clients_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "contracts_project_id_projects_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.templateId],
			foreignColumns: [contractTemplates.id],
			name: "contracts_template_id_contract_templates_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "contracts_created_by_users_id_fk"
		}).onDelete("cascade"),
]);

export const documentTemplates = pgTable("document_templates", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	type: varchar({ length: 50 }).notNull(),
	name: varchar({ length: 255 }).notNull(),
	templateData: text("template_data").notNull(),
	variables: text(),
	isActive: boolean("is_active").default(true),
	createdBy: varchar("created_by").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "document_templates_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "document_templates_created_by_users_id_fk"
		}).onDelete("cascade"),
]);

export const freelancerProfiles = pgTable("freelancer_profiles", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	userId: varchar("user_id").notNull(),
	businessName: varchar("business_name", { length: 255 }),
	tagline: varchar({ length: 500 }),
	bio: text(),
	website: varchar({ length: 255 }),
	hourlyRate: numeric("hourly_rate", { precision: 8, scale:  2 }),
	currency: varchar({ length: 3 }).default('USD'),
	skills: text(),
	availability: varchar({ length: 50 }).default('available'),
	timezone: varchar({ length: 100 }).default('UTC'),
	taxId: varchar("tax_id", { length: 50 }),
	taxIdType: varchar("tax_id_type", { length: 20 }),
	businessAddress: text("business_address"),
	isActive: boolean("is_active").default(true),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "freelancer_profiles_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const generatedDocuments = pgTable("generated_documents", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	templateId: varchar("template_id"),
	entityType: varchar("entity_type", { length: 50 }).notNull(),
	entityId: varchar("entity_id", { length: 255 }).notNull(),
	filename: varchar({ length: 255 }).notNull(),
	fileUrl: varchar("file_url", { length: 500 }).notNull(),
	fileSize: integer("file_size"),
	generatedBy: varchar("generated_by").notNull(),
	generatedAt: timestamp("generated_at", { mode: 'string' }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.templateId],
			foreignColumns: [documentTemplates.id],
			name: "generated_documents_template_id_document_templates_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.generatedBy],
			foreignColumns: [users.id],
			name: "generated_documents_generated_by_users_id_fk"
		}).onDelete("cascade"),
]);

export const invoiceItems = pgTable("invoice_items", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	invoiceId: varchar("invoice_id").notNull(),
	description: text().notNull(),
	quantity: numeric().notNull(),
	unitPrice: numeric("unit_price").notNull(),
	amount: numeric().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.invoiceId],
			foreignColumns: [invoices.id],
			name: "invoice_items_invoice_id_invoices_id_fk"
		}).onDelete("cascade"),
]);

export const expenseCategories = pgTable("expense_categories", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	isActive: boolean("is_active").default(true),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "expense_categories_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
]);

export const invoicePayments = pgTable("invoice_payments", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	invoiceId: varchar("invoice_id").notNull(),
	amount: numeric({ precision: 10, scale:  2 }).notNull(),
	paymentDate: timestamp("payment_date", { mode: 'string' }).notNull(),
	paymentMethod: varchar("payment_method", { length: 50 }),
	transactionId: varchar("transaction_id", { length: 255 }),
	notes: text(),
}, (table) => [
	foreignKey({
			columns: [table.invoiceId],
			foreignColumns: [invoices.id],
			name: "invoice_payments_invoice_id_invoices_id_fk"
		}).onDelete("cascade"),
]);

export const invoiceTaxes = pgTable("invoice_taxes", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	invoiceId: varchar("invoice_id").notNull(),
	taxRateId: varchar("tax_rate_id").notNull(),
	taxableAmount: numeric("taxable_amount", { precision: 10, scale:  2 }).notNull(),
	taxAmount: numeric("tax_amount", { precision: 10, scale:  2 }).notNull(),
	description: varchar({ length: 255 }),
}, (table) => [
	foreignKey({
			columns: [table.invoiceId],
			foreignColumns: [invoices.id],
			name: "invoice_taxes_invoice_id_invoices_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.taxRateId],
			foreignColumns: [taxRates.id],
			name: "invoice_taxes_tax_rate_id_tax_rates_id_fk"
		}).onDelete("restrict"),
]);

export const integrations = pgTable("integrations", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	provider: varchar({ length: 50 }).notNull(),
	name: varchar({ length: 255 }).notNull(),
	configData: text("config_data"),
	isActive: boolean("is_active").default(true),
	lastSyncAt: timestamp("last_sync_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "integrations_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
]);

export const fileUploads = pgTable("file_uploads", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	uploadedBy: varchar("uploaded_by").notNull(),
	filename: varchar({ length: 255 }).notNull(),
	originalName: varchar("original_name", { length: 255 }).notNull(),
	filePath: varchar("file_path", { length: 500 }).notNull(),
	fileSize: integer("file_size").notNull(),
	mimeType: varchar("mime_type", { length: 100 }).notNull(),
	entityType: varchar("entity_type", { length: 50 }),
	entityId: varchar("entity_id", { length: 255 }),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "file_uploads_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.uploadedBy],
			foreignColumns: [users.id],
			name: "file_uploads_uploaded_by_users_id_fk"
		}).onDelete("cascade"),
]);

export const organizationInvitations = pgTable("organization_invitations", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	email: varchar({ length: 255 }).notNull(),
	invitedById: varchar("invited_by_id").notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	acceptedAt: timestamp("accepted_at", { mode: 'string' }),
	rejectedAt: timestamp("rejected_at", { mode: 'string' }),
	inviteToken: varchar("invite_token", { length: 255 }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "organization_invitations_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.invitedById],
			foreignColumns: [users.id],
			name: "organization_invitations_invited_by_id_users_id_fk"
		}).onDelete("cascade"),
	unique("organization_invitations_invite_token_unique").on(table.inviteToken),
]);

export const organizationMembers = pgTable("organization_members", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	userId: varchar("user_id").notNull(),
	role: varchar({ length: 255 }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "organization_members_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "organization_members_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const paymentGateways = pgTable("payment_gateways", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	provider: varchar({ length: 50 }).notNull(),
	name: varchar({ length: 255 }).notNull(),
	isActive: boolean("is_active").default(true),
	isDefault: boolean("is_default").default(false),
	configData: text("config_data"),
	supportedCurrencies: text("supported_currencies"),
	supportedMethods: text("supported_methods"),
	webhookUrl: varchar("webhook_url", { length: 500 }),
	webhookSecret: varchar("webhook_secret", { length: 255 }),
	lastSyncAt: timestamp("last_sync_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "payment_gateways_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
]);

export const invoices = pgTable("invoices", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	clientId: varchar("client_id").notNull(),
	projectId: varchar("project_id"),
	number: varchar({ length: 255 }).notNull(),
	title: varchar({ length: 255 }),
	date: timestamp({ mode: 'string' }).notNull(),
	dueDate: timestamp("due_date", { mode: 'string' }).notNull(),
	subtotal: numeric({ precision: 10, scale:  2 }).notNull(),
	taxAmount: numeric("tax_amount", { precision: 10, scale:  2 }).default('0'),
	discountAmount: numeric("discount_amount", { precision: 10, scale:  2 }).default('0'),
	totalAmount: numeric("total_amount", { precision: 10, scale:  2 }).notNull(),
	currency: varchar({ length: 3 }).default('USD'),
	status: varchar({ length: 20 }).default('draft').notNull(),
	notes: text(),
	terms: text(),
	paidAt: timestamp("paid_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
}, (table) => [
	index("idx_invoices_client_status").using("btree", table.clientId.asc().nullsLast().op("text_ops"), table.status.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "invoices_client_id_clients_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "invoices_project_id_projects_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "invoices_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	unique("invoices_number_unique").on(table.number),
]);

export const organizations = pgTable("organizations", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	name: varchar({ length: 255 }).notNull(),
	ownerId: varchar("owner_id").notNull(),
	description: text(),
	avatar: varchar({ length: 255 }),
}, (table) => [
	foreignKey({
			columns: [table.ownerId],
			foreignColumns: [users.id],
			name: "organizations_owner_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const lateFees = pgTable("late_fees", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	invoiceId: varchar("invoice_id").notNull(),
	feeAmount: numeric("fee_amount", { precision: 10, scale:  2 }).notNull(),
	appliedDate: timestamp("applied_date", { mode: 'string' }).notNull(),
	reason: text(),
}, (table) => [
	foreignKey({
			columns: [table.invoiceId],
			foreignColumns: [invoices.id],
			name: "late_fees_invoice_id_invoices_id_fk"
		}).onDelete("cascade"),
]);

export const projectSprints = pgTable("project_sprints", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	projectId: varchar("project_id").notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	startDate: timestamp("start_date", { mode: 'string' }).notNull(),
	endDate: timestamp("end_date", { mode: 'string' }).notNull(),
	status: varchar({ length: 20 }).default('planned'),
	goal: text(),
	order: integer().default(0),
}, (table) => [
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "project_sprints_project_id_projects_id_fk"
		}).onDelete("cascade"),
]);

export const paymentTransactions = pgTable("payment_transactions", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	invoiceId: varchar("invoice_id").notNull(),
	gatewayId: varchar("gateway_id").notNull(),
	externalTransactionId: varchar("external_transaction_id", { length: 255 }).notNull(),
	amount: numeric({ precision: 10, scale:  2 }).notNull(),
	currency: varchar({ length: 3 }).notNull(),
	status: varchar({ length: 20 }).notNull(),
	paymentMethod: varchar("payment_method", { length: 50 }),
	gatewayFee: numeric("gateway_fee", { precision: 10, scale:  2 }),
	netAmount: numeric("net_amount", { precision: 10, scale:  2 }),
	processedAt: timestamp("processed_at", { mode: 'string' }),
	failureReason: text("failure_reason"),
	metadata: text(),
}, (table) => [
	foreignKey({
			columns: [table.invoiceId],
			foreignColumns: [invoices.id],
			name: "payment_transactions_invoice_id_invoices_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.gatewayId],
			foreignColumns: [paymentGateways.id],
			name: "payment_transactions_gateway_id_payment_gateways_id_fk"
		}).onDelete("restrict"),
]);

export const projectComments = pgTable("project_comments", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	projectId: varchar("project_id").notNull(),
	userId: varchar("user_id").notNull(),
	parentId: varchar("parent_id"),
	comment: text().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "project_comments_project_id_projects_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "project_comments_user_id_users_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.parentId],
			foreignColumns: [table.id],
			name: "project_comments_parent_id_project_comments_id_fk"
		}).onDelete("cascade"),
]);

export const projectTaskComments = pgTable("project_task_comments", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	taskId: varchar("task_id").notNull(),
	userId: varchar("user_id").notNull(),
	parentId: varchar("parent_id"),
	comment: text().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.taskId],
			foreignColumns: [projectTasks.id],
			name: "project_task_comments_task_id_project_tasks_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "project_task_comments_user_id_users_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.parentId],
			foreignColumns: [table.id],
			name: "project_task_comments_parent_id_project_task_comments_id_fk"
		}).onDelete("cascade"),
]);

export const projectTemplates = pgTable("project_templates", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	defaultBudget: numeric("default_budget", { precision: 10, scale:  2 }),
	defaultCurrency: varchar("default_currency", { length: 3 }).default('USD'),
	estimatedDuration: integer("estimated_duration"),
	isActive: boolean("is_active").default(true),
	createdBy: varchar("created_by").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "project_templates_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "project_templates_created_by_users_id_fk"
		}).onDelete("cascade"),
]);

export const projectTaskAttachments = pgTable("project_task_attachments", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	taskId: varchar("task_id").notNull(),
	userId: varchar("user_id").notNull(),
	name: varchar({ length: 255 }).notNull(),
	url: varchar({ length: 255 }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.taskId],
			foreignColumns: [projectTasks.id],
			name: "project_task_attachments_task_id_project_tasks_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "project_task_attachments_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const proposals = pgTable("proposals", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	clientId: varchar("client_id").notNull(),
	organizationId: varchar("organization_id").notNull(),
	title: varchar({ length: 255 }).notNull(),
	description: text().notNull(),
	scope: text(),
	deliverables: text(),
	timeline: text(),
	totalAmount: numeric("total_amount", { precision: 10, scale:  2 }).notNull(),
	currency: varchar({ length: 3 }).default('USD'),
	status: varchar({ length: 20 }).default('draft'),
	validUntil: timestamp("valid_until", { mode: 'string' }),
	sentAt: timestamp("sent_at", { mode: 'string' }),
	viewedAt: timestamp("viewed_at", { mode: 'string' }),
	respondedAt: timestamp("responded_at", { mode: 'string' }),
	notes: text(),
	createdBy: varchar("created_by").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "proposals_client_id_clients_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "proposals_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "proposals_created_by_users_id_fk"
		}).onDelete("cascade"),
]);

export const proposalApprovals = pgTable("proposal_approvals", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	proposalId: varchar("proposal_id").notNull(),
	approverUserId: varchar("approver_user_id"),
	approverEmail: varchar("approver_email", { length: 255 }),
	approverName: varchar("approver_name", { length: 255 }),
	status: varchar({ length: 20 }).default('pending'),
	comments: text(),
	approvedAt: timestamp("approved_at", { mode: 'string' }),
	order: integer().default(0),
}, (table) => [
	foreignKey({
			columns: [table.proposalId],
			foreignColumns: [proposals.id],
			name: "proposal_approvals_proposal_id_proposals_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.approverUserId],
			foreignColumns: [users.id],
			name: "proposal_approvals_approver_user_id_users_id_fk"
		}).onDelete("set null"),
]);

export const projectTasks = pgTable("project_tasks", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	projectId: varchar("project_id").notNull(),
	sprintId: varchar("sprint_id"),
	parentTaskId: varchar("parent_task_id"),
	statusId: varchar("status_id"),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	priority: varchar({ length: 20 }).default('medium'),
	estimatedHours: numeric("estimated_hours", { precision: 5, scale:  2 }),
	actualHours: numeric("actual_hours", { precision: 5, scale:  2 }),
	dueDate: timestamp("due_date", { mode: 'string' }),
	startedAt: timestamp("started_at", { mode: 'string' }),
	completedAt: timestamp("completed_at", { mode: 'string' }),
	order: integer().default(0),
}, (table) => [
	index("idx_tasks_project_status").using("btree", table.projectId.asc().nullsLast().op("text_ops"), table.statusId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "project_tasks_project_id_projects_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.sprintId],
			foreignColumns: [projectSprints.id],
			name: "project_tasks_sprint_id_project_sprints_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.parentTaskId],
			foreignColumns: [table.id],
			name: "project_tasks_parent_task_id_project_tasks_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.statusId],
			foreignColumns: [taskStatuses.id],
			name: "project_tasks_status_id_task_statuses_id_fk"
		}).onDelete("restrict"),
]);

export const proposalItems = pgTable("proposal_items", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	proposalId: varchar("proposal_id").notNull(),
	title: varchar({ length: 255 }).notNull(),
	description: text(),
	quantity: numeric({ precision: 8, scale:  2 }).default('1'),
	unitPrice: numeric("unit_price", { precision: 10, scale:  2 }).notNull(),
	amount: numeric({ precision: 10, scale:  2 }).notNull(),
	order: integer().default(0),
}, (table) => [
	foreignKey({
			columns: [table.proposalId],
			foreignColumns: [proposals.id],
			name: "proposal_items_proposal_id_proposals_id_fk"
		}).onDelete("cascade"),
]);

export const receipts = pgTable("receipts", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	vendorName: varchar("vendor_name", { length: 255 }).notNull(),
	amount: numeric({ precision: 10, scale:  2 }).notNull(),
	currency: varchar({ length: 3 }).default('USD'),
	receiptDate: timestamp("receipt_date", { mode: 'string' }).notNull(),
	categoryId: varchar("category_id"),
	description: text(),
	fileUrl: varchar("file_url", { length: 500 }),
	createdBy: varchar("created_by").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "receipts_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.categoryId],
			foreignColumns: [expenseCategories.id],
			name: "receipts_category_id_expense_categories_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "receipts_created_by_users_id_fk"
		}).onDelete("cascade"),
]);

export const projects = pgTable("projects", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	status: varchar({ length: 50 }).default('active').notNull(),
	startDate: timestamp("start_date", { mode: 'string' }),
	endDate: timestamp("end_date", { mode: 'string' }),
	budget: numeric({ precision: 10, scale:  2 }),
	currency: varchar({ length: 3 }).default('USD'),
	ownerId: varchar("owner_id").notNull(),
	organizationId: varchar("organization_id").notNull(),
	clientId: varchar("client_id").notNull(),
}, (table) => [
	index("idx_projects_org_client").using("btree", table.organizationId.asc().nullsLast().op("text_ops"), table.clientId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.ownerId],
			foreignColumns: [users.id],
			name: "projects_owner_id_users_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "projects_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "projects_client_id_clients_id_fk"
		}).onDelete("cascade"),
]);

export const projectTemplateTasks = pgTable("project_template_tasks", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	templateId: varchar("template_id").notNull(),
	parentTaskId: varchar("parent_task_id"),
	name: varchar({ length: 255 }).notNull(),
	description: text(),
	priority: varchar({ length: 20 }).default('medium'),
	estimatedHours: numeric("estimated_hours", { precision: 5, scale:  2 }),
	order: integer().default(0),
	dayOffset: integer("day_offset").default(0),
}, (table) => [
	foreignKey({
			columns: [table.templateId],
			foreignColumns: [projectTemplates.id],
			name: "project_template_tasks_template_id_project_templates_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.parentTaskId],
			foreignColumns: [table.id],
			name: "project_template_tasks_parent_task_id_project_template_tasks_id"
		}).onDelete("cascade"),
]);

export const taskDependencies = pgTable("task_dependencies", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	taskId: varchar("task_id").notNull(),
	dependsOnTaskId: varchar("depends_on_task_id").notNull(),
	dependencyType: varchar("dependency_type", { length: 20 }).default('finish_to_start'),
}, (table) => [
	foreignKey({
			columns: [table.taskId],
			foreignColumns: [projectTasks.id],
			name: "task_dependencies_task_id_project_tasks_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.dependsOnTaskId],
			foreignColumns: [projectTasks.id],
			name: "task_dependencies_depends_on_task_id_project_tasks_id_fk"
		}).onDelete("cascade"),
]);

export const teams = pgTable("teams", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	name: varchar({ length: 255 }).notNull(),
	ownerId: varchar("owner_id").notNull(),
	description: text(),
	avatar: varchar({ length: 255 }),
}, (table) => [
	foreignKey({
			columns: [table.ownerId],
			foreignColumns: [users.id],
			name: "teams_owner_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const sessions = pgTable("sessions", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	token: varchar({ length: 255 }).notNull(),
	ipAddress: varchar("ip_address", { length: 255 }),
	userAgent: varchar("user_agent", { length: 255 }),
	userId: varchar("user_id").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "sessions_user_id_users_id_fk"
		}).onDelete("cascade"),
	unique("sessions_token_unique").on(table.token),
]);

export const taskStatuses = pgTable("task_statuses", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	name: varchar({ length: 100 }).notNull(),
	color: varchar({ length: 7 }).default('#6B7280'),
	order: integer().default(0),
	isDefault: boolean("is_default").default(false),
	isCompleted: boolean("is_completed").default(false),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "task_statuses_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
]);

export const teamMembers = pgTable("team_members", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	teamId: varchar("team_id").notNull(),
	userId: varchar("user_id").notNull(),
	role: varchar({ length: 255 }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "team_members_team_id_teams_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "team_members_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const ticketAttachments = pgTable("ticket_attachments", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	ticketId: varchar("ticket_id").notNull(),
	messageId: varchar("message_id"),
	filename: varchar({ length: 255 }).notNull(),
	fileUrl: varchar("file_url", { length: 500 }).notNull(),
	fileSize: integer("file_size"),
	mimeType: varchar("mime_type", { length: 100 }),
}, (table) => [
	foreignKey({
			columns: [table.ticketId],
			foreignColumns: [supportTickets.id],
			name: "ticket_attachments_ticket_id_support_tickets_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.messageId],
			foreignColumns: [ticketMessages.id],
			name: "ticket_attachments_message_id_ticket_messages_id_fk"
		}).onDelete("cascade"),
]);

export const ticketMessages = pgTable("ticket_messages", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	ticketId: varchar("ticket_id").notNull(),
	userId: varchar("user_id").notNull(),
	message: text().notNull(),
	isInternal: boolean("is_internal").default(false),
}, (table) => [
	foreignKey({
			columns: [table.ticketId],
			foreignColumns: [supportTickets.id],
			name: "ticket_messages_ticket_id_support_tickets_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "ticket_messages_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const teamMemberPermissions = pgTable("team_member_permissions", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	teamMemberId: varchar("team_member_id").notNull(),
	permission: varchar({ length: 255 }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.teamMemberId],
			foreignColumns: [teamMembers.id],
			name: "team_member_permissions_team_member_id_team_members_id_fk"
		}).onDelete("cascade"),
]);

export const taxRates = pgTable("tax_rates", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationId: varchar("organization_id").notNull(),
	name: varchar({ length: 255 }).notNull(),
	rate: numeric({ precision: 5, scale:  4 }).notNull(),
	type: varchar({ length: 20 }).notNull(),
	country: varchar({ length: 2 }),
	state: varchar({ length: 50 }),
	city: varchar({ length: 100 }),
	zipCode: varchar("zip_code", { length: 20 }),
	isDefault: boolean("is_default").default(false),
	isActive: boolean("is_active").default(true),
	effectiveDate: timestamp("effective_date", { mode: 'string' }).notNull(),
	expiryDate: timestamp("expiry_date", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "tax_rates_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
]);

export const supportTickets = pgTable("support_tickets", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	clientId: varchar("client_id").notNull(),
	projectId: varchar("project_id"),
	title: varchar({ length: 255 }).notNull(),
	description: text().notNull(),
	priority: varchar({ length: 20 }).default('medium'),
	status: varchar({ length: 20 }).default('open'),
	assignedTo: varchar("assigned_to"),
	createdBy: varchar("created_by").notNull(),
	resolvedAt: timestamp("resolved_at", { mode: 'string' }),
	closedAt: timestamp("closed_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "support_tickets_client_id_clients_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "support_tickets_project_id_projects_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.assignedTo],
			foreignColumns: [users.id],
			name: "support_tickets_assigned_to_users_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [users.id],
			name: "support_tickets_created_by_users_id_fk"
		}).onDelete("cascade"),
]);

export const verifications = pgTable("verifications", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	identifier: varchar({ length: 255 }).notNull(),
	value: varchar({ length: 255 }).notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
});

export const users = pgTable("users", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	name: varchar({ length: 255 }).notNull(),
	email: varchar({ length: 255 }).notNull(),
	emailVerified: boolean("email_verified").default(false).notNull(),
	image: varchar({ length: 255 }),
	twoFactorEnabled: boolean("two_factor_enabled"),
	username: varchar({ length: 255 }),
	displayUsername: varchar("display_username", { length: 255 }),
	userType: varchar("user_type", { length: 20 }).default('internal').notNull(),
	clientId: varchar("client_id"),
	organizationId: varchar("organization_id"),
	isActive: boolean("is_active").default(true),
	lastLogin: timestamp("last_login", { mode: 'string' }),
}, (table) => [
	index("idx_users_org_type").using("btree", table.organizationId.asc().nullsLast().op("text_ops"), table.userType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.clientId],
			foreignColumns: [clients.id],
			name: "users_client_id_clients_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "users_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
	unique("users_email_unique").on(table.email),
	unique("users_username_unique").on(table.username),
]);

export const calendarEventAttendees = pgTable("calendar_event_attendees", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	eventId: varchar("event_id").notNull(),
	userId: varchar("user_id"),
	email: varchar({ length: 255 }),
	name: varchar({ length: 255 }),
	status: varchar({ length: 20 }).default('pending'),
	isOrganizer: boolean("is_organizer").default(false),
}, (table) => [
	foreignKey({
			columns: [table.eventId],
			foreignColumns: [calendarEvents.id],
			name: "calendar_event_attendees_event_id_calendar_events_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "calendar_event_attendees_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const contractSignatures = pgTable("contract_signatures", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	contractId: varchar("contract_id").notNull(),
	signerName: varchar("signer_name", { length: 255 }).notNull(),
	signerEmail: varchar("signer_email", { length: 255 }).notNull(),
	signatureData: text("signature_data"),
	signedAt: timestamp("signed_at", { mode: 'string' }).notNull(),
	ipAddress: varchar("ip_address", { length: 255 }),
}, (table) => [
	foreignKey({
			columns: [table.contractId],
			foreignColumns: [contracts.id],
			name: "contract_signatures_contract_id_contracts_id_fk"
		}).onDelete("cascade"),
]);

export const organizationMemberPermissions = pgTable("organization_member_permissions", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	organizationMemberId: varchar("organization_member_id").notNull(),
	permission: varchar({ length: 255 }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.organizationMemberId],
			foreignColumns: [organizationMembers.id],
			name: "organization_member_permissions_organization_member_id_organiza"
		}).onDelete("cascade"),
]);

export const paymentReminders = pgTable("payment_reminders", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	invoiceId: varchar("invoice_id").notNull(),
	reminderType: varchar("reminder_type", { length: 50 }).notNull(),
	sentAt: timestamp("sent_at", { mode: 'string' }).notNull(),
	status: varchar({ length: 20 }).default('sent'),
}, (table) => [
	foreignKey({
			columns: [table.invoiceId],
			foreignColumns: [invoices.id],
			name: "payment_reminders_invoice_id_invoices_id_fk"
		}).onDelete("cascade"),
]);

export const projectTaskAssignees = pgTable("project_task_assignees", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	taskId: varchar("task_id").notNull(),
	userId: varchar("user_id").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.taskId],
			foreignColumns: [projectTasks.id],
			name: "project_task_assignees_task_id_project_tasks_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "project_task_assignees_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const teamInvitations = pgTable("team_invitations", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	teamId: varchar("team_id").notNull(),
	email: varchar({ length: 255 }).notNull(),
	invitedById: varchar("invited_by_id").notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	acceptedAt: timestamp("accepted_at", { mode: 'string' }),
	rejectedAt: timestamp("rejected_at", { mode: 'string' }),
	inviteToken: varchar("invite_token", { length: 255 }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.teamId],
			foreignColumns: [teams.id],
			name: "team_invitations_team_id_teams_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.invitedById],
			foreignColumns: [users.id],
			name: "team_invitations_invited_by_id_users_id_fk"
		}).onDelete("cascade"),
	unique("team_invitations_invite_token_unique").on(table.inviteToken),
]);

export const twoFactors = pgTable("two_factors", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	secret: varchar({ length: 255 }).notNull(),
	backupCodes: varchar("backup_codes", { length: 255 }).notNull(),
	userId: varchar("user_id").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "two_factors_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const userOrganizations = pgTable("user_organizations", {
	id: varchar({ length: 255 }).primaryKey().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	userId: varchar("user_id").notNull(),
	organizationId: varchar("organization_id").notNull(),
	role: varchar({ length: 50 }).default('member').notNull(),
	isPrimary: boolean("is_primary").default(false),
	joinedAt: timestamp("joined_at", { mode: 'string' }).defaultNow().notNull(),
	isActive: boolean("is_active").default(true),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "user_organizations_user_id_users_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.organizationId],
			foreignColumns: [organizations.id],
			name: "user_organizations_organization_id_organizations_id_fk"
		}).onDelete("cascade"),
]);
